use std::{collections::{HashMap, HashSet}, path::Path};

use agent_common_service::model::{code_complete_model::CodeCompletionRequestBean, prompt_declaration::{PromptDeclaration, TypescriptFunctionDeclaration, TypescriptImportStatement, TypescriptPromptDeclaration}};
use agent_db::domain::code_kv_index_domain::{ClassInfo, CodeInfo, TypeScriptDeclaration, TypeScriptInfo};
use once_cell::sync::Lazy;
use oxc_resolver::Resolver;
use tree_sitter::{Node, Parser, Query, QueryCursor};

use super::{context::ensure_project_context, util::{get_file_ext, get_node_text, set_parser_language, LANGUAGE_JS, LANGUAGE_TS, LANGUAGE_TSX}};

static CODE_QUERY_MAP: Lazy<HashMap<&str, Query>> = Lazy::new(|| {
    let ts_code_info = include_str!("scm/ts_code_info.scm");
    let js_code_info = include_str!("scm/js_code_info.scm");
    let local_variable = include_str!("scm/local_variable.scm");
    let parameter = include_str!("scm/parameter.scm");
    HashMap::from([
        ("ts/code_info", Query::new(&LANGUAGE_TS, ts_code_info).unwrap()),
        ("ts/local_variable", Query::new(&LANGUAGE_TS, local_variable).unwrap()),
        ("ts/parameter", Query::new(&LANGUAGE_TS, parameter).unwrap()),
        ("tsx/code_info", Query::new(&LANGUAGE_TSX, ts_code_info).unwrap()),
        ("tsx/local_variable", Query::new(&LANGUAGE_TSX, local_variable).unwrap()),
        ("tsx/parameter", Query::new(&LANGUAGE_TSX, parameter).unwrap()),
        ("js/code_info", Query::new(&LANGUAGE_JS, js_code_info).unwrap()),
    ])
});

static ROOT_PATH_CONTEXT_QUERY_MAP: Lazy<HashMap<&str, Query>> = Lazy::new(|| {
    let arrow_function = include_str!("scm/root_path_context/arrow_function.scm");
    let class_declaration = include_str!("scm/root_path_context/class_declaration.scm");
    let js_class_declaration = include_str!("scm/root_path_context/js_class_declaration.scm");
    let function_declaration = include_str!("scm/root_path_context/function_declaration.scm");
    let generator_function_declaration = include_str!("scm/root_path_context/generator_function_declaration.scm");
    let method_definition = include_str!("scm/root_path_context/method_definition.scm");
    let program = include_str!("scm/root_path_context/program.scm");
    HashMap::from([
        ("ts/arrow_function", Query::new(&LANGUAGE_TS, arrow_function).unwrap()),
        ("ts/class_declaration", Query::new(&LANGUAGE_TS, class_declaration).unwrap()),
        ("ts/function_declaration", Query::new(&LANGUAGE_TS, function_declaration).unwrap()),
        ("ts/generator_function_declaration", Query::new(&LANGUAGE_TS, generator_function_declaration).unwrap()),
        ("ts/method_definition", Query::new(&LANGUAGE_TS, method_definition).unwrap()),
        ("ts/program", Query::new(&LANGUAGE_TS, program).unwrap()),
        ("tsx/arrow_function", Query::new(&LANGUAGE_TSX, arrow_function).unwrap()),
        ("tsx/class_declaration", Query::new(&LANGUAGE_TSX, class_declaration).unwrap()),
        ("tsx/function_declaration", Query::new(&LANGUAGE_TSX, function_declaration).unwrap()),
        ("tsx/generator_function_declaration", Query::new(&LANGUAGE_TSX, generator_function_declaration).unwrap()),
        ("tsx/method_definition", Query::new(&LANGUAGE_TSX, method_definition).unwrap()),
        ("tsx/program", Query::new(&LANGUAGE_TSX, program).unwrap()),
        ("js/class_declaration", Query::new(&LANGUAGE_JS, js_class_declaration).unwrap()),
        ("js/program", Query::new(&LANGUAGE_JS, program).unwrap()),
    ])
});

static ROOT_PATH_CONTEXT_TYPE_TO_USE: [&str; 6] = [
    "program",
    "arrow_function",
    "generator_function_declaration",
    "function_declaration",
    "method_definition",
    "class_declaration",
];

static ROOT_PATH_CONTEXT_FUNC_TYPE: [&str; 4] = [
    "arrow_function",
    "generator_function_declaration",
    "function_declaration",
    "method_definition",
];

pub fn extract_code_info(project_path: &String, file_path: &Path, source_code: &String) -> Option<CodeInfo> {
    let ext = get_file_ext(file_path)?;
    let is_js = ext == "js";
    let mut parser = Parser::new();
    set_parser_language(&mut parser, &ext)?;

    let source_code_byte = source_code.as_bytes();
    let tree = parser.parse(source_code, None)?;
    let query = CODE_QUERY_MAP.get(format!("{}/{}", &ext, "code_info").as_str())?;
    let mut query_cursor = QueryCursor::new();
    let matches = query_cursor.matches(query, tree.root_node(), source_code_byte);

    let mut class_list = vec![];
    let mut ts_info = TypeScriptInfo::default();
    let import_sources: HashSet<String> = HashSet::new();
    let mut types = vec![];
    let mut functions = vec![];

    // let context = ensure_project_context(project_path);
    // let context = context.as_ref();

    matches.into_iter().for_each(|m| {
        let captures = m.captures;
        let mut import_source_node = None;
        let mut identifier_node = None;
        let mut declaration_node = None;
        let mut is_default = false;

        captures.iter().for_each(|c| {
            match query.capture_names()[c.index as usize] {
                "import.source" => {
                    import_source_node = Some(c.node);
                },
                "identifier" => {
                    identifier_node = Some(c.node);
                },
                "export_default" => {
                    is_default = true;
                },
                "declaration" => {
                    declaration_node = Some(c.node);
                }
                _ => (),
            };
        });

        // import source 目前不参与相关片段计算，没啥作用，暂时先注释掉
        // import_source_node
        //     .zip(context)
        //     .map(|(node, context)| {
        //         if let Some(resolved_path) = get_import_source_path(&node, file_path, source_code_byte, &context.resolver) {
        //             import_sources.insert(resolved_path);
        //         }
        //     });

        declaration_node
            .map(|declaration_node| {
                let name = match identifier_node {
                    Some(node) if !is_default => node.utf8_text(source_code_byte).ok()?,
                    _ => "default",
                };
                let kind = declaration_node.kind();
                match kind {
                    "interface_declaration" | "type_alias_declaration" => {
                        types.push(TypeScriptDeclaration {
                            name: name.into(),
                            code_struct: declaration_node.utf8_text(source_code_byte).ok()?.into(),
                        });
                    }
                    "function_declaration" | "generator_function_declaration" | "arrow_function" | "function_expression" | "generator_function" => {
                        let body: Node<'_> = declaration_node.child_by_field_name("body")?;
                        let mut code_struct = get_node_text(Some(&declaration_node), source_code_byte)?;
                        code_struct.replace_range((body.start_byte() - declaration_node.start_byte())..(body.end_byte() - declaration_node.start_byte()), "{ }");
                        functions.push(TypeScriptDeclaration {
                            name: name.into(),
                            code_struct,
                        });
                    }
                    "lexical_declaration" | "variable_declaration" => {
                        let body = declaration_node
                            .named_children(&mut declaration_node.walk())
                            .find(|n| n.kind() == "variable_declarator")
                            ?.child_by_field_name("value")
                            ?.child_by_field_name("body")?;
                        let mut code_struct = get_node_text(Some(&declaration_node), source_code_byte)?;
                        code_struct.replace_range((body.start_byte() - declaration_node.start_byte())..(body.end_byte() - declaration_node.start_byte()), "{ }");
                        functions.push(TypeScriptDeclaration {
                            name: name.into(),
                            code_struct,
                        });
                    }
                    "class_declaration" | "abstract_class_declaration" | "class" => {
                        if let Some(class_info) = extract_class_code_info(declaration_node, source_code_byte, is_js) {
                            class_list.push(class_info);
                        }
                    }
                    _ => (),
                };

                None as Option::<()>
            });
    });

    if !import_sources.is_empty() {
        ts_info.import_source_set = Some(import_sources);
    }
    if !types.is_empty() {
        ts_info.type_vec = Some(types);
    }
    if !functions.is_empty() {
        ts_info.function_vec = Some(functions);
    }

    Some(CodeInfo {
        class_list: if class_list.is_empty() { None } else { Some(class_list) },
        extend_typescript: Some(ts_info),
        ..CodeInfo::default()
    })
}

pub fn get_prompt_declaration(bean: &CodeCompletionRequestBean) -> Option<PromptDeclaration> {
    let ext = get_file_ext(&Path::new(&bean.fileUrl))?;
    let mut parser = Parser::new();
    set_parser_language(&mut parser, &ext)?;

    let mut source_code = String::with_capacity(bean.prompt.len() + bean.suffix.len());
    source_code.push_str(bean.prompt.as_str());
    source_code.push_str(bean.suffix.as_str());
    let source_code_byte = source_code.as_bytes();

    let tree = parser.parse(&source_code, None)?;
    let ast_path = get_ast_path(tree.root_node(), bean.prompt.len());

    let mut prompt_declaration = PromptDeclaration::new();
    let mut implements_class_name_set = HashSet::new();
    let mut field_set = HashSet::new();
    let mut function_declarations = vec![];
    let mut import_map: HashMap<String, String> = HashMap::new();
    let mut import_list = vec![];

    let context = if let Some(project_url) = &bean.projectUrl {
        ensure_project_context(project_url)
    } else {
        None
    };
    let context = context.as_ref();

    // 获取最近的函数节点，用于提取本地变量
    let mut local_var_type_vec = None;
    // js 没有类型，无法拿到局部变量类型信息
    if ext.as_str() != "js" {
        let nearest_func_node = ast_path.iter().rev().find(|node| ROOT_PATH_CONTEXT_FUNC_TYPE.contains(&node.kind()));
        if let Some(nearest_func_node) = nearest_func_node {
            if let Some(body) = nearest_func_node.child_by_field_name("body") {
                local_var_type_vec = extract_local_var(body, source_code_byte, &ext, bean.prompt.len());
            }
        }
    }

    ast_path.into_iter()
        .filter(|node| ROOT_PATH_CONTEXT_TYPE_TO_USE.contains(&node.kind()))
        .for_each(|node| {
            let query = match ROOT_PATH_CONTEXT_QUERY_MAP.get(format!("{}/{}", &ext, node.kind()).as_str()) {
                Some(v) => v,
                None => return (),
            };
            let mut query_cursor = QueryCursor::new();
            let matches = query_cursor.matches(query, node, source_code_byte);
            matches.into_iter().for_each(|m| {
                let captures = m.captures;
                let mut import_source = None;
                let mut import_specifier_name = None;
                let mut import_specifier_alias = None;
                // let mut param_type_vec = vec![];
                let mut function_declaration = TypescriptFunctionDeclaration::default();
                captures.iter().for_each(|c| {
                    let text = match get_node_text(Some(&c.node), source_code_byte) {
                        Some(v) => v,
                        None => return ()
                    };
                    match query.capture_names()[c.index as usize] {
                        "base_class" => {
                            prompt_declaration.extends_class = Some(text);
                        }
                        "implement" => {
                            implements_class_name_set.insert(text);
                        }
                        "field_type" => {
                            field_set.insert(text);
                        }
                        // "param_type" => {
                        //     param_type_vec.push(text);
                        // },
                        "formal_parameters" => {
                            // param_type_vec.push(text);
                            function_declaration.param_type_vec = extract_parameter(c.node, source_code_byte, &ext);
                        },
                        "return_type" => {
                            function_declaration.return_type = Some(text);
                        },
                        "import_source" => {
                            let text = c.node.utf8_text(source_code_byte);
                            if let Ok(text) = text {                                
                                if let Some(resolved_path) = import_map.get(text) {
                                    // import_declaration.source = (*resolved_path).to_owned();
                                    import_source = Some((*resolved_path).to_owned())
                                } else if let Some(context) = context {
                                    if let Some(resolved_path) = get_import_source_path(text, &Path::new(bean.fileUrl.as_str()), &context.resolver) {
                                        import_map.insert(text.to_owned(), resolved_path.clone());
                                        // import_declaration.source = resolved_path;
                                        import_source = Some(resolved_path)
                                    }
                                }
                            }
                        }
                        "import_specifier_name" => {
                            import_specifier_name = Some(text);
                        },
                        "import_specifier_alias" => {
                            import_specifier_alias = Some(text);
                        },
                        "import_default" => {
                            import_specifier_name = Some("default".into());
                            import_specifier_alias = Some(text);
                        }
                        _ => (),
                    }
                });
                if let (Some(import_source), Some(import_specifier_name)) = (import_source, import_specifier_name) {
                    import_list.push((
                        import_source,
                        (import_specifier_name.clone(), import_specifier_alias.unwrap_or(import_specifier_name)),
                    ));
                }
                if function_declaration.param_type_vec.is_some() || function_declaration.return_type.is_some() {
                    function_declarations.push(function_declaration);
                }
            });
        });

    if !implements_class_name_set.is_empty() {
        prompt_declaration.implements_class_name_set = Some(implements_class_name_set);
    }
    if !field_set.is_empty() {
        prompt_declaration.field_set = Some(field_set);
    }
    if !import_list.is_empty() || !function_declarations.is_empty() {
        let mut extend_typescript = TypescriptPromptDeclaration {
            import_vec: None,
            function_declaration: None,
            function_param_type_vec: None,
        };

        if !import_list.is_empty() {
            let import_vec: Vec<TypescriptImportStatement> = import_list
                .into_iter()
                .fold(HashMap::new(), |mut map, item| {
                    let group = map.entry(item.0.clone()).or_insert(vec![]);
                    group.push(item.1);
                    map
                })
                .into_iter()
                .map(|(source, name_set)| TypescriptImportStatement {
                    source,
                    name_vec: name_set.into_iter().collect(),
                })
                .collect();

            extend_typescript.import_vec = Some(import_vec)
        }

        if !function_declarations.is_empty() {
            let mut declaration = function_declarations[function_declarations.len() - 1].clone();
            declaration.local_var_type_vec = local_var_type_vec;
            extend_typescript.function_declaration = Some(declaration);
            
            let mut type_vec = vec![];
            function_declarations.iter().for_each(|item| {
                if let Some(param_type_vec) = &item.param_type_vec {
                    type_vec.push(param_type_vec.clone());
                }
            });
            if !type_vec.is_empty() {
                extend_typescript.function_param_type_vec = Some(type_vec);
            }
        }

        prompt_declaration.extend_typescript = Some(extend_typescript);
    }

    Some(prompt_declaration)

}

fn get_ast_path(node: Node, cursor_index: usize) -> Vec<Node> {
    let mut path = vec![node];
    while path[path.len() - 1].child_count() > 0 {
        let mut found_child = false;
        let current_node = &path[path.len() - 1];
        for node in current_node.children(&mut current_node.walk()) {
            if node.start_byte() <= cursor_index && node.end_byte() >= cursor_index {
                path.push(node);
                found_child = true;
                break;
            }
        }
        if !found_child {
            break;
        }
    }
    path
}

fn get_import_source_path(specifier: &str, file_path: &Path, resolver: &Resolver) -> Option<String> {
    let dir = file_path.parent()?;
    let resolution = resolver.resolve(dir, specifier).ok()?;
    let resolved_path = resolution.into_path_buf().into_os_string().into_string().ok()?;
    Some(resolved_path)
}

fn extract_class_code_info(class_node: Node, source: &[u8], is_js: bool) -> Option<ClassInfo> {
    let mut cursor = class_node.walk();
    let mut class_info = ClassInfo {
        class_type: "class".into(),
        class_name: "".into(),
        extend_class_name: None,
        implements_class_name_set: None,
        field_name_class_map: None,
        code_struct: get_node_text(Some(&class_node), source)?,
    };

    let mut implements_class_name_set = HashSet::new();
    let mut field_name_class_map = HashMap::new();
    let mut to_remove: Vec<(usize, usize, &str)> = Vec::new();

    for node in class_node.children(&mut cursor) {
        match node.kind() {
            "type_identifier" | "identifier" => {
                class_info.class_name = node.utf8_text(source).ok()?.to_owned();
            }
            "class_heritage" => {
                let mut cursor = node.walk();
                for node in node.children(&mut cursor) {
                    match node.kind() {
                        "identifier" if is_js => {
                            class_info.extend_class_name = Some(node.utf8_text(source).ok()?.to_owned());
                        }
                        "extends_clause" if !is_js => {
                            if let Some(text) = get_node_text(node.child_by_field_name("value").as_ref(), source) {
                                class_info.extend_class_name =Some(text.to_owned());
                            }
                        }
                        "implements_clause" if !is_js => {
                            let mut cursor = node.walk();
                            for node in node.children(&mut cursor) {
                                match node.kind() {
                                    "type_identifier" => {
                                        if let Some(text) = get_node_text(Some(&node), source) {
                                            implements_class_name_set.insert(text);
                                        }
                                    }
                                    "generic_type" => {
                                        if let Some(text) = get_node_text(node.child_by_field_name("name").as_ref(), source) {
                                            implements_class_name_set.insert(text);
                                        }
                                    }
                                    _ => (),
                                }
                            }
                        }
                        _ => (),
                    };
                }
            },
            "class_body" => {
                let mut cursor = node.walk();
                for node in node.children(&mut cursor) {
                    match node.kind() {
                        "public_field_definition" if !is_js => {
                            node
                                .child_by_field_name("type")
                                .map(|node| {
                                    node.children(&mut node.walk()).find(|node| node.kind() == "type_identifier")
                                })
                                .map(|type_node| {
                                    let name_node = node.child_by_field_name("name");
                                    get_node_text(type_node.as_ref(), source)
                                        .zip(get_node_text(name_node.as_ref(), source))
                                        .map(|(type_text, name_text)| {
                                            field_name_class_map.insert(name_text, type_text);
                                        });
                                });
                        }
                        // 移除具体实现，仅保留签名
                        "method_definition" => {
                            if let Some(body) = node.child_by_field_name("body") {
                                to_remove.push((body.start_byte() - class_node.start_byte(), body.end_byte() - class_node.start_byte(), "{ }"));
                            }
                        },
                        // 移除注释
                        "comment" => {
                            to_remove.push((node.start_byte() - class_node.start_byte(), node.end_byte() - class_node.start_byte(), ""));
                        },
                        _ => (),
                    }
                }
            },
            _ => (),
        }
    }

    // 按 start_byte 倒序，这样删除时就不会改变后面的相对位置，否则 replace 会出错
    to_remove.sort_by(|a, b| b.0.cmp(&a.0));
    for (start, end, replacement) in to_remove {
        class_info.code_struct.replace_range(start..end, replacement);
    }
    // 移除空行
    class_info.code_struct = class_info.code_struct.as_str().lines().filter(|text| !text.trim().is_empty()).collect::<Vec<&str>>().join("\n");

    if !implements_class_name_set.is_empty() {
        class_info.implements_class_name_set = Some(implements_class_name_set);
    }
    if !field_name_class_map.is_empty() {
        class_info.field_name_class_map = Some(field_name_class_map);
    }

    Some(class_info)
}

fn extract_parameter(node: Node, source: &[u8], ext: &String) -> Option<Vec<String>> {
    let query = CODE_QUERY_MAP.get(format!("{}/{}", ext, "parameter").as_str())?;
    let mut query_cursor = QueryCursor::new();
    let captures = query_cursor.captures(query, node, source);

    let params: Vec<String> = captures
        .map(|(m, i)| {
            m.captures[i].node.utf8_text(source).unwrap_or_default().to_owned()
        })
        .filter(|s| !s.is_empty())
        .collect();

    if params.is_empty() {
        None
    } else {
        Some(params)
    }
}

fn extract_local_var(node: Node, source: &[u8], ext: &String, pos: usize) -> Option<Vec<String>> {
    let query = CODE_QUERY_MAP.get(format!("{}/{}", ext, "local_variable").as_str())?;
    let mut query_cursor = QueryCursor::new();
    let captures = query_cursor.captures(query, node, source);

    let type_identifiers: Vec<String> = captures
        .map(|(m, i)| {
            let node = m.captures[i].node;
            if node.end_byte() <= pos {
                m.captures[i].node.utf8_text(source).unwrap_or_default().to_owned()
            } else {
                "".to_owned()
            }
        })
        .filter(|s| !s.is_empty())
        .collect();

    Some(type_identifiers)
}

#[cfg(test)]
mod tests {
    use std::env::current_dir;

    use super::*;

    #[test]
    fn test_extract_code_info() {
        let cwd = current_dir().unwrap();
        let cwd = cwd.parent().unwrap();
        let dir = Path::new(file!()).parent().unwrap();
        let file_path = cwd.join(dir).join("fixtures/src/pages/home/<USER>").into_os_string().into_string().unwrap();
        let project_path = cwd.join(dir).join("fixtures").into_os_string().into_string().unwrap();
        let source_code = r"
import '@/app';
import './home';

// INode
export interface INode {
  val: string,
  next?: INode,
}

// type object
export type obj = Record<string, any>

// f1
export function f1(): string { return '1' }

/**
 * f2
 */
export function* f2() { console.log(1) }

// f3
export const f3 = (a: string, b: string) => a + b
export const f4 = function() { return 1 }
export const f5 = function* () {return 2 }

// 类
export class Util extends BaseUtil {
  // v1
  v1: INode;

  v2 = 2;  // v2

  /**
   * get v1 value
   */
  get fn1(): number {
    return v1
  }

  // fn2
  fn2(a: string) {
    console.log(a)
  }
}

// 抽象类
export abstract class Util2 implements IUtil {
  fn() {}
}

interface Props {
  count: number
}

export default (props: Props) => {
  return <div></div>
}

const add = (a: number, b: number): number => a + b
const inc = (a: number) => a + 1

export { add, inc }
".trim();

        let code_info = extract_code_info(&project_path, file_path.as_ref(), &source_code.to_owned());
        assert_eq!(code_info, Some(CodeInfo {
            code_struct: "".into(),
            full_qualified_name: None,
            import_set: None,
            class_type: None,
            class_name: None,
            extend_class_name: None,
            implements_class_name_set: None,
            fild_name_class_map: None,
            class_list: Some(vec![
                ClassInfo {
                    class_type: "class".into(),
                    class_name: "Util".into(),
                    extend_class_name: Some("BaseUtil".into()),
                    implements_class_name_set: None,
                    field_name_class_map: Some(HashMap::from([
                        ("v1".into(), "INode".into())
                    ])),
                    code_struct: "class Util extends BaseUtil {\n  v1: INode;\n  v2 = 2;  \n  get fn1(): number { }\n  fn2(a: string) { }\n}".into(),
                },
                ClassInfo {
                    class_type: "class".into(),
                    class_name: "Util2".into(),
                    extend_class_name: None,
                    implements_class_name_set: Some(HashSet::from(
                        ["IUtil".into()]
                    )),
                    field_name_class_map: None,
                    code_struct: "abstract class Util2 implements IUtil {\n  fn() { }\n}".into(),
                },
            ]),
            extend_typescript: Some(
                TypeScriptInfo {
                    import_source_set: None,
                    type_vec: Some(vec![
                        TypeScriptDeclaration {
                            name: "INode".into(),
                            code_struct: "interface INode {\n  val: string,\n  next?: INode,\n}".into(),
                        },
                        TypeScriptDeclaration {
                            name: "obj".into(),
                            code_struct: "type obj = Record<string, any>".into(),
                        },
                    ]),
                    function_vec: Some(vec![
                        TypeScriptDeclaration {
                            name: "f1".into(),
                            code_struct: "function f1(): string { }".into(),
                        },
                        TypeScriptDeclaration {
                            name: "f2".into(),
                            code_struct: "function* f2() { }".into(),
                        },
                        TypeScriptDeclaration {
                            name: "f3".into(),
                            code_struct: "const f3 = (a: string, b: string) => { }".into(),
                        },
                        TypeScriptDeclaration {
                            name: "f4".into(),
                            code_struct: "const f4 = function() { }".into(),
                        },
                        TypeScriptDeclaration {
                            name: "f5".into(),
                            code_struct: "const f5 = function* () { }".into(),
                        },
                        TypeScriptDeclaration {
                            name: "default".into(),
                            code_struct: "(props: Props) => { }".into(),
                        },
                        TypeScriptDeclaration {
                            name: "add".into(),
                            code_struct: "const add = (a: number, b: number): number => { }".into(),
                        },
                        TypeScriptDeclaration {
                            name: "inc".into(),
                            code_struct: "const inc = (a: number) => { }".into(),
                        },
                    ]),
                }
            ),
        }))
    }

    #[test]
    fn test_js_extract_code_info() {
        let cwd = current_dir().unwrap();
        let cwd = cwd.parent().unwrap();
        let dir = Path::new(file!()).parent().unwrap();
        let file_path = cwd.join(dir).join("fixtures/src/pages/home/<USER>").into_os_string().into_string().unwrap();
        let project_path = cwd.join(dir).join("fixtures").into_os_string().into_string().unwrap();
        let source_code = r"
import '@/app';
import './util';

// f1
export function f1() { return '1' }

/**
 * f2
 */
export function* f2() { console.log(1) }

// f3
export const f3 = (a, b) => a + b
export const f4 = function() { return 1 }
export const f5 = function* () {return 2 }

// 类
export class Util extends BaseUtil {
  v2 = 2;  // v2

  /**
   * get v1 value
   */
  get fn1() {
    return v1
  }

  // fn2
  fn2(a) {
    console.log(a)
  }
}

export default (props) => {
  return <div></div>
}

const add = (a, b) => a + b
const inc = (a) => a + 1

export { add, inc }
".trim();

        let code_info = extract_code_info(&project_path, file_path.as_ref(), &source_code.to_owned());
        assert_eq!(code_info, Some(CodeInfo {
            code_struct: "".into(),
            full_qualified_name: None,
            import_set: None,
            class_type: None,
            class_name: None,
            extend_class_name: None,
            implements_class_name_set: None,
            fild_name_class_map: None,
            class_list: Some(vec![
                ClassInfo {
                    class_type: "class".into(),
                    class_name: "Util".into(),
                    extend_class_name: Some("BaseUtil".into()),
                    implements_class_name_set: None,
                    field_name_class_map: None,
                    code_struct: "class Util extends BaseUtil {\n  v2 = 2;  \n  get fn1() { }\n  fn2(a) { }\n}".into(),
                },
            ]),
            extend_typescript: Some(
                TypeScriptInfo {
                    import_source_set: None,
                    type_vec: None,
                    function_vec: Some(vec![
                        TypeScriptDeclaration {
                            name: "f1".into(),
                            code_struct: "function f1() { }".into(),
                        },
                        TypeScriptDeclaration {
                            name: "f2".into(),
                            code_struct: "function* f2() { }".into(),
                        },
                        TypeScriptDeclaration {
                            name: "f3".into(),
                            code_struct: "const f3 = (a, b) => { }".into(),
                        },
                        TypeScriptDeclaration {
                            name: "f4".into(),
                            code_struct: "const f4 = function() { }".into(),
                        },
                        TypeScriptDeclaration {
                            name: "f5".into(),
                            code_struct: "const f5 = function* () { }".into(),
                        },
                        TypeScriptDeclaration {
                            name: "default".into(),
                            code_struct: "(props) => { }".into(),
                        },
                        TypeScriptDeclaration {
                            name: "add".into(),
                            code_struct: "const add = (a, b) => { }".into(),
                        },
                        TypeScriptDeclaration {
                            name: "inc".into(),
                            code_struct: "const inc = (a) => { }".into(),
                        },
                    ]),
                }
            ),
        }))
    }

    #[test]
    fn test_get_prompt_declaration() {
        let cwd = current_dir().unwrap();
        let cwd = cwd.parent().unwrap();
        let dir = Path::new(file!()).parent().unwrap();
        let file_path = cwd.join(dir).join("fixtures/src/pages/home/<USER>").into_os_string().into_string().unwrap();
        let project_path = cwd.join(dir).join("fixtures").into_os_string().into_string().unwrap();

        let prompt = r"
import * as app from '@/app';
import Home, { IHomeProps } from './home';
import { sum, log as Log } from './util';

// 类
export class Util extends BaseUtil implements IUtil {
  logger: ILogger;

  register(key: IKey, views: View, options: any): IComponent {
    let manager: IManager = this.getManager()
    let cache: ICache
    if (
    ".trim();

        let suffix = r"
)
    
    let log: ILogger = this.logger
  }

  // fn2
  fn2(a: string) {
    console.log(a)
  }
}
        ".trim();

        dbg!(&file_path, &project_path);

        let bean = CodeCompletionRequestBean {
            projectUrl: Some(project_path.clone()),
            prompt: prompt.into(),
            suffix: suffix.into(),
            userToken: "".into(),
            language: "".into(),
            fileUrl: file_path.into(),
            repo: None,
            pluginVersion: "".into(),
            ideVersion: "".into(),
            recordInfo: None,
            similarSnippets: None,
            relatedSnippets: None,
            timeOut: 0,
            productType: "".into(),
            sessionId: "".into(),
            skipFilter: false,
            fileNameSuffix: None,
            openFixSafety: None,
            completionType: None,
            sourceCodePrefix: None,
            sourceCodeSuffix: None,
            originalCode: None,
            fileDiffs: None,
        };

        let prompt_declaration = get_prompt_declaration(&bean);
        dbg!(&prompt_declaration);
    }

    #[test]
    fn test_js_get_prompt_declaration() {
        let cwd = current_dir().unwrap();
        let cwd = cwd.parent().unwrap();
        let dir = Path::new(file!()).parent().unwrap();
        let file_path = cwd.join(dir).join("fixtures/src/pages/about/index.jsx").into_os_string().into_string().unwrap();
        let project_path = cwd.join(dir).join("fixtures").into_os_string().into_string().unwrap();

        let prompt = r"
import * as app from '@/app';
import Home from './about';
import { sum, log as Log } from './util';

// 类
export class Util extends BaseUtil {
  register(key, views, options) {
    let manager = this.getManager()
    let cache
    if (
    ".trim();

        let suffix = r"
)
    
    let log = this.logger
  }

  // fn2
  fn2(a) {
    console.log(a)
  }
}
        ".trim();

        dbg!(&file_path, &project_path);

        let bean = CodeCompletionRequestBean {
            projectUrl: Some(project_path.clone()),
            prompt: prompt.into(),
            suffix: suffix.into(),
            userToken: "".into(),
            language: "".into(),
            fileUrl: file_path.into(),
            repo: None,
            pluginVersion: "".into(),
            ideVersion: "".into(),
            recordInfo: None,
            similarSnippets: None,
            relatedSnippets: None,
            timeOut: 0,
            productType: "".into(),
            sessionId: "".into(),
            skipFilter: false,
            fileNameSuffix: None,
            openFixSafety: None,
            completionType: None,
            sourceCodePrefix: None,
            sourceCodeSuffix: None,
            originalCode: None,
            fileDiffs: None,
        };

        let prompt_declaration = get_prompt_declaration(&bean);
        dbg!(&prompt_declaration);
    }
}
