use agent_db::domain::code_chat_domain::ChatRelatedCodeModel;
use serde::{Deserialize, Serialize};


//仓库信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RepoGitInfo {
    //仓库root地址
    pub projectUrl: Option<String>,
    //仓库git 地址
    pub gitAddress: Option<String>,
    //分支名称
    pub branch: Option<String>,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct HealthRequestBean {
    //工号
    pub userId: Option<String>,
    //userToken
    pub userToken: Option<String>,
    //插件版本
    pub pluginVersion: Option<String>,
    //用户使用的产品
    pub productType: Option<String>,
    //ide版本
    pub ideVersion: Option<String>,
    //agent版本
    pub agentVersion: Option<String>,
    //仓库信息列表
    pub repoGitInfo: Option<Vec<RepoGitInfo>>,
}
//Merkle Tree 查询
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RequestMerkleTreeData {
    //用户信息（工号等）
    pub userId: String,
    //会话 session ID
    pub sessionId: String,
    //确定为 AntCode 仓库时传递，用于 UCS 快速 fork
    pub repoUrl: Option<String>,
    //确定为 AntCode 仓库时传递，用于 UCS 快速 fork
    pub branch: Option<String>,
    //确定为 AntCode 仓库时传递，用于 UCS 快速 fork
    pub commit: Option<String>,
    //获取某一个目录下的子树（默认为根目录）
    pub relativePath: Option<String>,
    //获取某一个节点及其下子树（默认为根节点）
    pub nodeHash: Option<String>,
    //目标子树的最大深度，查询到叶节点或者最大深度为止，默认为完整树
    pub layer: Option<usize>,
}

impl Default for RequestMerkleTreeData {
    fn default() -> Self {
        RequestMerkleTreeData {
            userId: "".to_string(),
            sessionId: "".to_string(),
            repoUrl: None,
            branch: None,
            commit: None,
            relativePath: None,
            nodeHash: None,
            layer: None,
        }
    }
}
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RequestMerkleTreeResponseData {
    //JSON 序列化后的完整 merkle tree
    pub merkleTree: Option<String>,
    //限流参数：单个批次上传最大文件数量
    pub batchSize: usize,
    //限流参数：上传批次之间的最小间隔时间, 单位
    pub interval: f32,
}

//Merkle Tree 上传
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UpdateMerkleTreeData {
    //用户信息（工号等）
    pub userId: String,
    //会话 session ID
    pub sessionId: String,
    //确定为 AntCode 仓库时传递，用于 UCS 快速 fork
    pub repoUrl: Option<String>,
    //确定为 AntCode 仓库时传递，用于 UCS 快速 fork
    pub branch: Option<String>,
    //确定为 AntCode 仓库时传递，用于 UCS 快速 fork
    pub commit: Option<String>,
    //待更新 merkle tree 信息
    pub data: MerkleTreeNode,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UpdateMerkleTreeResponseData {
    //代码根目录 tree hash
    pub rootHash: String,
    //JSON 序列化后的完整 merkle tree
    pub merkleTree: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MerkleTreeNode {
    //代码根目录 tree hash
    pub rootHash: String,
    //JSON 序列化后的完整 merkle tree
    pub merkleTree: String
}
//变更提交
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UploadDiffFileData {
    //用户信息（工号等）
    pub userId: String,
    //会话 session ID
    pub sessionId: String,
    //确定为 AntCode 仓库时传递，用于 UCS 快速 fork
    pub repoUrl: Option<String>,
    //确定为 AntCode 仓库时传递，用于 UCS 快速 fork
    pub branch: Option<String>,
    //确定为 AntCode 仓库时传递，用于 UCS 快速 fork
    pub commit: Option<String>,
    //待更新 merkle tree 信息
    pub data: DiffFileDataWrapper,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UploadDiffFileDataResponse {
    //上传成功文件列表
    pub files: Vec<String>,
    //限流参数：单个批次上传最大文件数量
    pub batchSize: String,
    //限流参数：上传批次之间的最小间隔时间
    pub interval: usize,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DiffFileDataWrapper {
    //实时 RAG 使用的 embedding 模型名称
    pub embeddingModel: String,
    //待索引文件内容、元信息
    pub files:Vec<DiffFileData>,

}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct DiffFileData {
    //文件相对路径
    pub relativePath: String,
    //文件 hash
    pub hash: Option<String>,
    //文件操作类型：modify、add、delete
    pub operation: String,
    //文件完整内容
    pub content: String,
}
//代码检索
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SearchFromRemoteRequestData {
    //用户信息（工号等）
    pub userId: String,
    //会话 session ID
    pub sessionId: String,
    //用户查询语句
    pub query: String,
    //查询结果数量限制，默认为 10
    pub limit: Option<usize>,
    //查询分数（越大越好）阈值限制，默认为 0.2
    pub threshold: Option<usize>,
    //确定为 AntCode 仓库时传递，用于 UCS 快速 fork
    pub repoUrl: Option<String>,
    //确定为 AntCode 仓库时传递，用于 UCS 快速 fork
    pub branch: Option<String>,
    //确定为 AntCode 仓库时传递，用于 UCS 快速 fork
    pub commit: Option<String>,
    //增量实时 RAG 的代码、配置信息
    pub data: Option<LocalDiffDataForSearchFromRemote>,
}
impl Default for SearchFromRemoteRequestData {
    fn default() -> Self {
        SearchFromRemoteRequestData {
            userId: "".to_string(),
            sessionId: "".to_string(),
            query: "".to_string(),
            limit: None,
            threshold: None,
            repoUrl: None,
            branch: None,
            commit: None,
            data: None,
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SearchFromRemoteResponse {
    pub snippets: Vec<ChatRelatedCodeModel>
}
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LocalDiffDataForSearchFromRemote {
    //实时 RAG 使用的 embedding 模型名称，默认与工作空间 embedding 模型保持一致
    pub embeddingModel: Option<String>,
    //UCS + 实时RAG 结果重排模型，默认为CODEFUSE_PLUGIN_QWEN25_CODER_32B_INSTRUCTION_CODEFUSEVAT
    pub rerankModel: Option<String>,
    //增量实时 RAG 的代码文件列表
    pub files: Option<Vec<DiffFileData>>
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MerkleTreeRelateConfig {
    //是否使用merkle tree相关功能
    pub use_remote_index: bool,
    //生成merkle tree时，扫描文件的间隔时间，控制资源的使用
    pub sleep_time: usize,
}

impl Default for MerkleTreeRelateConfig {
    fn default() -> Self {
        MerkleTreeRelateConfig{
            use_remote_index: false,
            sleep_time: 0,
        }
    }
}